<script setup lang="ts">
import Menu from 'primevue/menu'
import type { MenuItem } from 'primevue/menuitem'
import { menuRoutes } from '@/router'
import { type RouteRecordRaw } from 'vue-router'
import { reactive } from 'vue'
import AppBreadcurmb from './AppBreadcurmb.vue'

function resolveMenuItem(routes: RouteRecordRaw[], parentPath = '') {
  const items: MenuItem[] = []
  routes.forEach((route) => {
    if (route.meta?.hidden) return

    const to = parentPath && route.path ? parentPath + '/' + route.path : parentPath || route.path
    const item = {
      ...(route.meta as MenuItem),
      to: to,
    }
    if (route.children) {
      item['items'] = resolveMenuItem(route.children, to)
    }
    items.push(item)
  })
  return items
}

const items = reactive(resolveMenuItem(menuRoutes))
</script>

<template>
  <div class="flex h-[100vh] space-x-3 p-3">
    <Menu :model="items">
      <template #item="{ item, props }">
        <RouterLink v-if="item.to" v-slot="{ href, navigate }" :to="item.to" custom>
          <a :href="href" v-bind="props.action" @click="navigate">
            <span :class="item.icon" />
            <span class="ml-2">{{ item.label }}</span>
          </a>
        </RouterLink>
      </template>
    </Menu>
    <div class="grow">
      <div class="flex h-full flex-col">
        <div class="border-surface mb-4 border-b">
          <AppBreadcurmb></AppBreadcurmb>
        </div>
        <main class="rounded-border border-surface grow overflow-y-scroll">
          <RouterView></RouterView>
        </main>
      </div>
    </div>
  </div>
</template>
