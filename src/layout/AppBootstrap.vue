<script lang="ts" setup>
import { refreshToken } from '@/api'
import AppLayout from './AppLayout.vue'
import { onBeforeUnmount, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { RouteName } from '@/router'
import { LOGIN_NEXT_KEY } from '@/variables'
import type { AxiosError } from 'axios'

const loading = ref(true)
const router = useRouter()
const route = useRoute()

let interval: number | undefined
onMounted(() => {
  refreshToken()
    .then(() => {
      loading.value = false
      interval = setInterval(refreshToken, 3 * 60 * 1000)
    })
    .catch((err: AxiosError) => {
      if (err.response?.status === 401) {
        return router.replace({
          name: RouteName.Login,
          query: { [LOGIN_NEXT_KEY]: route.fullPath },
        })
      }
      return Promise.reject(err)
    })
})

onBeforeUnmount(() => {
  clearInterval(interval)
})
</script>

<template>
  <AppLayout v-if="!loading"></AppLayout>
  <template v-else>
    <div class="flex h-screen w-screen items-center justify-center">
      <i class="pi pi-spin pi-spinner text-3xl!"></i>
    </div>
  </template>
</template>
