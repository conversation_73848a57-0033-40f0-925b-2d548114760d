import axios, { AxiosError } from 'axios'
import { Configuration, DashboardApi } from './api-client'
import { API_ERROR_EVENT } from './variables'

declare module 'axios' {
  export interface AxiosRequestConfig {
    skipCheckStatusCodes?: number[]
  }
}

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
})

let accessToken: string | undefined

axiosInstance.interceptors.request.use((config) => {
  if (accessToken) {
    config.headers.Authorization = `Bearer ${accessToken}`
  }
  return config
})
axiosInstance.interceptors.response.use(
  function (response) {
    return response
  },
  function (error: AxiosError) {
    if (error.response) {
      if (!error.config?.skipCheckStatusCodes?.includes(error.response.status)) {
        window.dispatchEvent(new CustomEvent(API_ERROR_EVENT, { detail: error.response }))
      }
    }
    return Promise.reject(error)
  },
)

export async function login(username: string, password: string, cFTurnstileResponse: string) {
  return api
    .dashAuthTokenPost(cFTurnstileResponse, username, password, { skipCheckStatusCodes: [401] })
    .then((resp) => {
      accessToken = resp.data.access_token
    })
}

export function refreshToken() {
  return api.dashAuthTokenRefreshGet({ skipCheckStatusCodes: [401] }).then((resp) => {
    accessToken = resp.data.access_token
  })
}

const configuration = new Configuration()
export const api = new DashboardApi(configuration, undefined, axiosInstance)
