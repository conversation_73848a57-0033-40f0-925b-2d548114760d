import { useConfirm as usePrimeConfirm } from 'primevue/useconfirm'

export function useConfirm() {
  const confirm = usePrimeConfirm()

  return {
    require(option: { target: HTMLElement; message: string; accept: () => void }) {
      confirm.require({
        target: option.target,
        message: option.message,
        icon: 'pi pi-exclamation-triangle',
        acceptProps: {
          label: '是',
        },
        rejectProps: {
          label: '否',
          severity: 'secondary',
          outlined: true,
        },
        accept: option.accept,
      })
    },
  }
}
