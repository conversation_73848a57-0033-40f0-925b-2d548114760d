<script lang="ts" setup>
import DataTable from 'primevue/datatable'
import { Paginator } from 'primevue'

const props = defineProps<{
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  value?: readonly any[]
  rows?: number
  totalRecords?: number
  loading: boolean
}>()
const currentPage = defineModel<number>('currentPage', { default: 1 })
const emit = defineEmits(['update:currentPage'])
</script>

<template>
  <DataTable :value="props.value" tableStyle="min-width: 50rem" :loading="props.loading">
    <slot />
    <template #empty>
      <div class="flex min-h-24 items-center justify-center">
        {{ props.value?.length === 0 ? '没有数据' : '加载中...' }}
      </div>
    </template>
    <template
      #footer
      v-if="
        props.rows !== undefined &&
        props.totalRecords !== undefined &&
        props.totalRecords > props.rows
      "
    >
      <Paginator
        :first="(currentPage - 1) * (rows ?? 0)"
        @page="
          ({ page }) => {
            emit('update:currentPage', page + 1)
          }
        "
        :rows="props.rows"
        :totalRecords="props.totalRecords"
      ></Paginator>
    </template>
  </DataTable>
</template>
