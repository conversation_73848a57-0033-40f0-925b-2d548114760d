<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { Form, FormField, FormFieldMessage } from '@/components/primevue'
import Select from 'primevue/select'
import { z } from 'zod'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { Button, InputText, useToast } from 'primevue'
import type { FormSubmitEvent } from '@primevue/forms'
import type {
  DashTopicpublishedGet200ResponsePublishedInnerClient,
  DashTopicsGet200ResponseTopicsInner,
} from '@/api-client'
import { api } from '@/api'
import type { AxiosError } from 'axios'

const emit = defineEmits({
  publishSuccess() {
    return true
  },
})
const props = defineProps<{ topic: DashTopicsGet200ResponseTopicsInner }>()

const toast = useToast()

const clientOptions = ref<DashTopicpublishedGet200ResponsePublishedInnerClient[]>()
onMounted(() => {
  api.dashClientsGet().then((resp) => {
    clientOptions.value = resp.data.clients
  })
})

const resolver = zodResolver(
  z.object({
    clientId: z.string().nonempty('请选择客户端'),
    title: z.string().min(1, '请输入标题').max(100, '标题长度不能超过 100 个字符'),
  }),
)
function handleSubmit({ values, valid }: FormSubmitEvent) {
  if (!valid) return

  api
    .dashTopicTopicIdPublishClientIdPost(
      props.topic.id,
      values.clientId,
      { title: values.title },
      { skipCheckStatusCodes: [409] },
    )
    .then(() => {
      emit('publishSuccess')
    })
    .catch((err: AxiosError) => {
      if (err.response?.status === 409) {
        toast.add({
          severity: 'error',
          summary: '发布失败',
          detail: '该专题已发布到该客户端',
          life: 3000,
        })
      } else {
        return Promise.reject(err)
      }
    })
}
</script>

<template>
  <Form :resolver="resolver" @submit="handleSubmit">
    <FormField v-slot="$field" name="clientId" initialValue="">
      <label>客户端</label>
      <Select
        :options="clientOptions"
        optionLabel="name"
        optionValue="id"
        placeholder="请选择客户端"
      >
        <template #option="slotProps">
          <div>{{ slotProps.option.name }}</div>
        </template>
      </Select>
      <FormFieldMessage v-if="$field?.invalid">{{ $field.error?.message }}</FormFieldMessage>
    </FormField>
    <FormField v-slot="$field" name="title" initialValue="">
      <label>标题</label>
      <InputText placeholder="请输入标题"></InputText>
      <FormFieldMessage v-if="$field?.invalid">{{ $field.error?.message }}</FormFieldMessage>
    </FormField>
    <Button type="submit" label="发 布" icon="pi pi-send" fluid></Button>
  </Form>
</template>
