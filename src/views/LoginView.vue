<script setup lang="ts">
import InputText from 'primevue/inputtext'
import But<PERSON> from 'primevue/button'
import { onMounted, ref, watch } from 'vue'
import { login } from '@/api'
import { useRoute, useRouter } from 'vue-router'
import Message from 'primevue/message'
import { LOGIN_NEXT_KEY } from '@/variables'

const username = ref('')
const password = ref('')
const errMessage = ref('')

watch([username, password], () => {
  errMessage.value = ''
})

const router = useRouter()
const route = useRoute()

const loginLoading = ref(false)
async function handleLogin() {
  if (loginLoading.value) return

  loginLoading.value = true
  login(username.value, password.value, turnstileToken.value)
    .then(() => {
      const next = route.query[LOGIN_NEXT_KEY]
      if (typeof next === 'string') {
        router.replace(next)
      } else {
        router.replace('/')
      }
    })
    .catch(() => {
      errMessage.value = '用户名或密码错误'
    })
    .finally(() => {
      loginLoading.value = false
    })
}

declare global {
  interface Window {
    onloadTurnstileCallback: () => void
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    turnstile: any
  }
}

const turnstileToken = ref('')

onMounted(() => {
  window.onloadTurnstileCallback = function () {
    window.turnstile.render('#cf-turnstile', {
      sitekey: import.meta.env.VITE_CLOUDFLARE_TURNSTILE_SITEKEY || '1x00000000000000000000AA',
      callback: function (token: string) {
        turnstileToken.value = token
      },
      size: 'flexible',
    })
  }
  const script = document.createElement('script')
  script.src =
    'https://challenges.cloudflare.com/turnstile/v0/api.js?onload=onloadTurnstileCallback'
  document.head.appendChild(script)
})
</script>

<template>
  <div class="px-6 py-20 md:px-12 lg:px-20">
    <form
      class="bg-surface-0 dark:bg-surface-900 mx-auto flex w-full max-w-xl flex-col gap-8 rounded-2xl p-8 shadow-sm md:p-12"
      @submit.prevent="handleLogin"
    >
      <div class="flex flex-col items-center gap-4">
        <div class="flex w-full flex-col items-center gap-2">
          <div
            class="text-surface-900 dark:text-surface-0 w-full text-center text-2xl leading-tight font-semibold"
          >
            👏🏻欢迎回来
          </div>
        </div>
      </div>
      <div class="flex w-full flex-col gap-6">
        <div class="flex w-full flex-col gap-2">
          <label
            for="username"
            class="text-surface-900 dark:text-surface-0 leading-normal font-medium"
            >用户名</label
          >
          <InputText
            id="username"
            v-model.trim="username"
            type="text"
            placeholder="用户名"
            class="w-full rounded-lg px-3 py-2 shadow-sm"
          />
          <Message v-if="errMessage" severity="error" size="small" variant="simple">{{
            errMessage
          }}</Message>
        </div>
        <div class="flex w-full flex-col gap-2">
          <label
            for="password"
            class="text-surface-900 dark:text-surface-0 leading-normal font-medium"
            >密码</label
          >
          <InputText
            id="password"
            v-model.trim="password"
            type="password"
            placeholder="密码"
            class="w-full rounded-lg px-3 py-2 shadow-sm"
          />
        </div>
      </div>
      <div id="cf-turnstile"></div>
      <Button
        type="submit"
        label="登 录"
        icon="pi pi-user"
        class="flex w-full items-center justify-center gap-2 rounded-lg py-2"
        :disabled="!(username && password && turnstileToken)"
        :loading="loginLoading"
      >
        <template #icon>
          <i class="pi pi-user !text-base !leading-normal" />
        </template>
      </Button>
    </form>
  </div>
</template>
