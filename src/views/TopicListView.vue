<script lang="ts" setup>
import Column from 'primevue/column'
import { api } from '@/api'
import { onMounted, reactive, ref, watch } from 'vue'
import But<PERSON> from 'primevue/button'
import Dialog from 'primevue/dialog'
import { InputText } from 'primevue'
import { Form, type FormSubmitEvent } from '@primevue/forms'
import PagingTable from '@/components/PagingTable.vue'
import { RouteName } from '@/router'
import { useConfirm } from '@/helpers/useconfirm'
import TopicPublishView from '@/views/TopicPublishView.vue'
import type { DashTopicsGet200Response, DashTopicsGet200ResponseTopicsInner } from '@/api-client'

const confirm = useConfirm()

const viewData = ref<DashTopicsGet200Response>()
const tableLoading = ref(true)

const currentPage = ref(1)
async function updateViewData() {
  tableLoading.value = true
  api
    .dashTopicsGet({ page: currentPage.value, page_size: 8 })
    .then((resp) => {
      viewData.value = resp.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
watch(currentPage, () => {
  updateViewData()
})
onMounted(() => {
  updateViewData()
})

const dialogVisible = ref(false)
const initialValues = reactive({
  comment: '',
})
const editingData = ref<DashTopicsGet200ResponseTopicsInner>()
const formStatus = ref<'creation' | 'editing'>()

const publishDialogVisible = ref(false)
const currentOperatingTopic = ref<DashTopicsGet200ResponseTopicsInner>()

function handleSubmit(event: FormSubmitEvent) {
  if (!event.valid) return

  const values = event.values as { comment: string }
  if (formStatus.value === 'editing') {
    if (!editingData.value) return
    api.dashTopicsTopicIdPut(editingData.value.id, values).then(() => {
      dialogVisible.value = false
      updateViewData()
    })
    return
  } else if (formStatus.value === 'creation') {
    api.dashTopicsPost(values).then(() => {
      dialogVisible.value = false
      updateViewData()
    })
  }
}

function handleClickCreation() {
  formStatus.value = 'creation'
  initialValues.comment = ''
  dialogVisible.value = true
}

function handleClickEditing(data: DashTopicsGet200ResponseTopicsInner) {
  initialValues.comment = data.comment
  editingData.value = data
  formStatus.value = 'editing'
  dialogVisible.value = true
}
</script>

<template>
  <div class="space-y-4">
    <div class="flex justify-end">
      <Button icon="pi pi-plus" label="新建专题" @click="handleClickCreation"></Button>
    </div>
    <PagingTable
      :value="viewData?.topics"
      v-model:currentPage="currentPage"
      :rows="viewData?.current_page_size"
      :totalRecords="viewData?.total"
      :loading="tableLoading"
    >
      <Column field="id" header="ID"></Column>
      <Column field="comment" header="描述"></Column>
      <Column header="壁纸">
        <template #body="slotProps: { data: DashTopicsGet200ResponseTopicsInner }">
          <RouterLink
            class="underline"
            :to="{ name: RouteName.TopicWallpapers, params: { id: slotProps.data.id } }"
          >
            {{ slotProps.data.wallpaper_count }}
          </RouterLink>
        </template>
      </Column>
      <Column header="已发布">
        <template #body="slotProps: { data: DashTopicsGet200ResponseTopicsInner }">
          <RouterLink class="underline" :to="{ name: RouteName.TopicPublished }">
            {{ slotProps.data.published_count }}
          </RouterLink>
        </template>
      </Column>
      <Column header="操作">
        <template #body="slotProps: { data: DashTopicsGet200ResponseTopicsInner }">
          <div class="space-x-2">
            <Button
              rounded
              size="small"
              icon="pi pi-pencil"
              @click="handleClickEditing(slotProps.data)"
            ></Button>
            <Button
              icon="pi pi-trash"
              severity="danger"
              rounded
              size="small"
              @click="
                (event) => {
                  confirm.require({
                    target: event.currentTarget as HTMLElement,
                    message: '确定要删除吗？',
                    accept: () => {
                      api.dashTopicsTopicIdDelete(slotProps.data.id).then(() => {
                        updateViewData()
                      })
                    },
                  })
                }
              "
            ></Button>
            <Button
              icon="pi pi-send"
              rounded
              severity="help"
              size="small"
              @click="
                () => {
                  currentOperatingTopic = slotProps.data
                  publishDialogVisible = true
                }
              "
            ></Button>
          </div>
        </template>
      </Column>
    </PagingTable>
  </div>

  <Dialog
    v-model:visible="dialogVisible"
    modal
    :header="
      (() => {
        switch (formStatus) {
          case 'creation':
            return '新建专题'
          case 'editing':
            return '编辑专题'
        }
      })()
    "
    :style="{ width: '25rem' }"
  >
    <Form :initialValues @submit="handleSubmit" class="flex w-full flex-col gap-4">
      <div>
        <InputText name="comment" placeholder="描述" fluid />
      </div>
      <div class="flex justify-end gap-2">
        <Button
          type="button"
          label="取消"
          severity="secondary"
          @click="dialogVisible = false"
        ></Button>
        <Button type="submit" label="保存" />
      </div>
    </Form>
  </Dialog>

  <!-- publish -->
  <Dialog
    modal
    header="发布专题"
    v-model:visible="publishDialogVisible"
    :style="{ width: '30rem' }"
  >
    <template v-if="currentOperatingTopic">
      <TopicPublishView
        :topic="currentOperatingTopic"
        @publishSuccess="
          () => {
            updateViewData()
            publishDialogVisible = false
          }
        "
      />
    </template>
  </Dialog>
</template>
