<script lang="ts" setup>
import PagingTable from '@/components/PagingTable.vue'
import { useConfirm } from '@/helpers/useconfirm'
import Column from 'primevue/column'
import Button from 'primevue/button'
import { onBeforeMount, ref, watch } from 'vue'
import type {
  DashTopicpublishedGet200Response,
  DashTopicpublishedGet200ResponsePublishedInner,
} from '@/api-client'
import { api } from '@/api'

const confirm = useConfirm()

const viewData = ref<DashTopicpublishedGet200Response>()
const tableLoading = ref(true)
const currentPage = ref(1)
function updateViewData() {
  tableLoading.value = true
  api
    .dashTopicpublishedGet({
      page: currentPage.value,
      page_size: 20,
    })
    .then((resp) => {
      viewData.value = resp.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
onBeforeMount(() => {
  updateViewData()
})
watch(currentPage, () => {
  updateViewData()
})
</script>

<template>
  <div>
    <PagingTable
      :value="viewData?.published"
      :rows="viewData?.current_page_size"
      :total-records="viewData?.total"
      :loading="tableLoading"
    >
      <Column field="id" header="ID"></Column>
      <Column field="title" header="标题"></Column>
      <Column field="topic.comment" header="描述"></Column>
      <Column field="client.name" header="客户端"></Column>
      <Column header="发布时间">
        <template #body="slotProps: { data: DashTopicpublishedGet200ResponsePublishedInner }">
          {{ new Date(slotProps.data.published_at).toLocaleString() }}
        </template>
      </Column>
      <Column header="操作">
        <template #body="slotProps: { data: DashTopicpublishedGet200ResponsePublishedInner }">
          <div class="space-x-2">
            <Button
              icon="pi pi-trash"
              severity="danger"
              rounded
              size="small"
              @click="
                (event) => {
                  confirm.require({
                    target: event.currentTarget as HTMLElement,
                    message: '确定要删除吗？',
                    accept: () => {
                      api.dashTopicpublishedPublishedIdDelete(slotProps.data.id).then(() => {
                        updateViewData()
                      })
                    },
                  })
                }
              "
            ></Button>
          </div>
        </template>
      </Column>
    </PagingTable>
  </div>
</template>
